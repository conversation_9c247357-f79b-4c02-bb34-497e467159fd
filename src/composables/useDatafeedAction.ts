import { storeToRefs } from "pinia"
import { ref, watch, nextTick } from "vue"
import { commonStore } from "~/stores/commonStore"
import { getKlinesApi } from '~/api/order'

export default function useDatafeedAction(info) {
  const dataCache = new Map()
  const CACHE_DURATION = 2 * 60 * 1000
  const pairInfo = info
  const store = commonStore()
  const interval = ref('')
  const pair = ref('')
  const preObj = ref({})
  const { klineList, klineTicker, ticker } = storeToRefs(store)
  const resolutionMap: any = {
    1: '1m',
    // 3: '3m',
    5: '5m',
    15: '15m',
    30: '30m',
    60: '1h',
    120: '2h',
    240: '4h',
    360: '6h',
    480: '8h',
    720: '12h',
    '1D': '1d',
    // '3D': '3day',
    '1W': '1w',
    '1M': '1M'
  }

  const resolutionReMap: any = {
    'line': 1,
    '1m': 1,
    '5m': 5,
    '15m': 15,
    '30m': 30,
    '1h': 60,
    '2h': 120,
    '4h': 240,
    '6h': 360,
    '8h': 480,
    '12h': 720,
    '1d': '1D',
    '1w': '1W',
    '1M': '1M'
  }
  const subMap: any = {}
  let rafId: number | null = null

  function formatSymbol (symbol: string) {
    return symbol.toUpperCase()
  }

  const safeNumber = (value: any, fallback: number = 0): number => {
    const num = Number(value)
    return isNaN(num) || !isFinite(num) ? fallback : Math.abs(num)
  }

  // 根据周期对齐时间戳
  const alignTimestamp = (timestamp: number, resolution: string): number => {
    const date = new Date(timestamp)
    const minutes = date.getMinutes()
    const seconds = date.getSeconds()
    const milliseconds = date.getMilliseconds()

    // 清除秒和毫秒
    date.setSeconds(0, 0)

    switch (resolution) {
      case '1m':
        // 1分钟不需要额外对齐
        break
      case '5m':
        // 5分钟对齐到 0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55
        date.setMinutes(Math.floor(minutes / 5) * 5)
        break
      case '15m':
        // 15分钟对齐到 0, 15, 30, 45
        date.setMinutes(Math.floor(minutes / 15) * 15)
        break
      case '30m':
        // 30分钟对齐到 0, 30
        date.setMinutes(Math.floor(minutes / 30) * 30)
        break
      case '1h':
        // 1小时对齐到整点
        date.setMinutes(0)
        break
      case '2h':
        // 2小时对齐
        const hours = date.getHours()
        date.setHours(Math.floor(hours / 2) * 2, 0)
        break
      case '4h':
        // 4小时对齐
        const hours4 = date.getHours()
        date.setHours(Math.floor(hours4 / 4) * 4, 0)
        break
      case '1d':
        // 1天对齐到0点
        date.setHours(0, 0)
        break
      default:
        // 其他周期保持原样
        break
    }

    return date.getTime()
  }

  const getValidTimestamp = (newTime: any, key: string, resolution?: string): number => {
    const timestamp = Number(newTime)
    const lastTime = lastBarTime.value[key] || 0
    const now = Date.now()

    // 如果时间戳无效，使用当前时间
    if (!timestamp || isNaN(timestamp)) {
      const alignedNow = resolution ? alignTimestamp(now, resolution) : now
      lastBarTime.value[key] = alignedNow
      return alignedNow
    }

    // 根据周期对齐时间戳
    const alignedTimestamp = resolution ? alignTimestamp(timestamp, resolution) : timestamp

    // 如果这是第一次为这个key设置时间戳，直接使用对齐后的时间戳
    if (lastTime === 0) {
      lastBarTime.value[key] = alignedTimestamp
      return alignedTimestamp
    }

    // 如果新时间戳比上一个时间戳早，需要处理时间违规
    if (alignedTimestamp <= lastTime) {
      const timeDiff = lastTime - alignedTimestamp

      // 对于小的时间差异（如1-5分钟），这可能是正常的历史数据或网络延迟
      if (timeDiff <= 300000) { // 5分钟 = 300000毫秒
        // 直接使用对齐后的时间戳，但不更新lastBarTime以避免时间倒退
        return alignedTimestamp
      }

      // 对于较大的时间差异，使用修正后的时间
      const validTime = Math.max(now, lastTime + 60000)
      const alignedValidTime = resolution ? alignTimestamp(validTime, resolution) : validTime
      console.warn(`Time violation detected for ${key}. Using ${new Date(alignedValidTime).toISOString()} instead of ${new Date(alignedTimestamp).toISOString()}`)
      lastBarTime.value[key] = alignedValidTime
      return alignedValidTime
    }

    // 正常情况：新时间戳大于上一个时间戳
    lastBarTime.value[key] = alignedTimestamp
    return alignedTimestamp
  }
  let key = ''
  async function handleMonthlyData(symbolInfo: any, resolution: any, periodParams: any, onHistoryCallback: any, onErrorCallback: any) {
    const { firstDataRequest } = periodParams

    if (firstDataRequest && resolution === '1M') {
      if (klineList.value.length && klineTicker.value.currentPeriod === '1M' &&
          klineTicker.value.currentPair === symbolInfo.fullName) {
        preObj.value = klineList.value[0]
        onHistoryCallback(klineList.value, {noData: klineList.value.length === 0})
        return true
      }

      const waitForWebSocketData = () => {
        return new Promise((resolve) => {
          let attempts = 0
          const maxAttempts = 20
          const checkInterval = 100

          const checkData = () => {
            attempts++
            if (klineList.value.length && klineTicker.value.currentPeriod === '1M' &&
                klineTicker.value.currentPair === symbolInfo.fullName) {
              preObj.value = klineList.value[0]
              onHistoryCallback(klineList.value, {noData: klineList.value.length === 0})
              resolve(true)
            } else if (attempts >= maxAttempts) {
              resolve(false)
            } else {
              setTimeout(checkData, checkInterval)
            }
          }

          setTimeout(checkData, checkInterval)
        })
      }

      const hasWebSocketData = await waitForWebSocketData()
      return hasWebSocketData
    }
    return false
  }

  async function getBars(
    symbolInfo: any,
    resolution: any,
    periodParams: any,
    onHistoryCallback: any,
    onErrorCallback: any) {
    pair.value = symbolInfo.fullName
    interval.value = resolutionMap[resolution]
    key = `${symbolInfo.fullName}_#_${resolutionMap[resolution]}`
    const { from, to, firstDataRequest, countBack } = periodParams;  // `from` 和 `to` 用来确定需要获取的时间范围

    // 如果是首次请求且周期发生变化，清理相关缓存和时间戳
    if (firstDataRequest) {
      // 清理月度数据缓存
      if (resolution !== '1M') {
        const monthlyKey = `${symbolInfo.fullName}-1M-first`
        if (dataCache.has(monthlyKey)) {
          dataCache.delete(monthlyKey)
        }
        if (klineTicker.value.currentPeriod === '1M') {
          klineList.value = []
          klineTicker.value = {}
        }
      }

      // 清理不同周期的时间戳记录
      const currentKey = key
      Object.keys(lastBarTime.value).forEach(timeKey => {
        if (timeKey !== currentKey && timeKey.startsWith(symbolInfo.fullName)) {
          delete lastBarTime.value[timeKey]
        }
      })
    }

    if (resolution === '1M') {
      const handled = await handleMonthlyData(symbolInfo, resolution, periodParams, onHistoryCallback, onErrorCallback)
      if (handled) {
        return
      }
    }

    fetchHistoricalData(symbolInfo.fullName, resolution, from, to, firstDataRequest, countBack, onHistoryCallback, onErrorCallback);
  }
  const forceRefresh = ref(false)

  const clearCache = () => {
    dataCache.clear()
    lastCompleteBar.value = {}
    lastBarTime.value = {}
    monthlySubscriptionCache = {
      key: null,
      subscription: null,
      pair: null
    }
    klineList.value = []
    klineTicker.value = {}
  }

  const setForceRefresh = (force: boolean) => {
    forceRefresh.value = force
    if (force) {
      clearCache()
    }
  }
  async function fetchHistoricalData(symbol: string, resolution: string, from: number, to: number, firstDataRequest: Boolean, countBack: number, onHistoryCallback: any, onErrorCallback: any) {
    const mappedResolution = resolutionMap[resolution] || resolution
    const cacheKey = `${symbol}-${mappedResolution}-${firstDataRequest ? 'first' : from}`
    const cachedData = dataCache.get(cacheKey)
    const isMonthlyResolution = mappedResolution === '1M'
    const cacheTimeout = isMonthlyResolution ? (firstDataRequest ? 60 * 1000 : 30 * 1000) : CACHE_DURATION

    // 检查是否存在1M周期的缓存，如果存在且当前不是1M周期，则强制刷新
    const monthlyKey = `${symbol}-1M-first`
    const hasMonthlyCache = dataCache.has(monthlyKey)
    // 特别处理从1M切换到1m的情况
    const isFromMonthlyToMinute = hasMonthlyCache && mappedResolution === '1m' && firstDataRequest
    const shouldForceRefresh = forceRefresh.value || (hasMonthlyCache && !isMonthlyResolution && firstDataRequest) || isFromMonthlyToMinute

    if (cachedData && (Date.now() - cachedData.timestamp < cacheTimeout) && firstDataRequest && !shouldForceRefresh) {
      onHistoryCallback(cachedData.data, { noData: cachedData.data.length === 0 })
      return
    }

    if (forceRefresh.value) {
      forceRefresh.value = false
    }

    try {
      const now = Date.now()
      const requestLimit = isMonthlyResolution && firstDataRequest ?
        Math.max(countBack > 300 ? 300 : countBack, 50) :
        (countBack > 300 ? 300 : countBack)

      const { data } = await getKlinesApi({
        symbol: formatSymbol(symbol),
        market: formatSymbol(symbol).includes('_SWAP') ? 'lpc' : 'spot',
        time_frame: resolutionMap[resolution],
        before: firstDataRequest ? now : preObj.value.time,
        limit: requestLimit,
        origin:1,
      })

      if (data) {
        const formattedData = data.e.map(item => {
          const time = Number(item[0])
          const open = Math.abs(Number(item[1]))
          const high = Math.abs(Number(item[2]))
          const low = Math.abs(Number(item[3]))
          const close = Math.abs(Number(item[4]))
          const volume = Math.abs(Number(item[5]))
          return { time, open, high, low, close, volume }
        })

        if (formattedData.length > 0) {
          preObj.value = formattedData[0]
          if (firstDataRequest) {
            dataCache.set(cacheKey, {
              data: formattedData,
              timestamp: now
            })
          }

          if (isMonthlyResolution && firstDataRequest && formattedData.length > 0) {
            klineList.value = formattedData
            klineTicker.value = {
              ...formattedData[formattedData.length - 1],
              currentPair: symbol,
              currentPeriod: '1M'
            }
          }
        }

        onHistoryCallback(formattedData, { noData: formattedData.length === 0 })
      } else {
        onErrorCallback('No data received from API')
      }
    } catch (error) {
      onErrorCallback(error)
    }
  }
  let lastCompleteBar = ref({})
  let lastBarTime = ref({})

  let monthlySubscriptionCache = {
    key: null,
    subscription: null,
    pair: null
  }

  function handleMonthlyRealtimeUpdate(val1: any, val2: any) {
    if (interval.value !== '1M') return false

    const monthlyKey = `${pair.value}_#_1M`
    const last = (val1[pair.value] || {}).last

    let monthlySubscription = null
    let subscriptionKey = null

    if (monthlySubscriptionCache.pair === pair.value &&
        monthlySubscriptionCache.key &&
        subMap[monthlySubscriptionCache.key]) {
      monthlySubscription = monthlySubscriptionCache.subscription
      subscriptionKey = monthlySubscriptionCache.key
    } else {
      if (subMap[monthlyKey]) {
        monthlySubscription = subMap[monthlyKey]
        subscriptionKey = monthlyKey
      } else {
        Object.keys(subMap).forEach(key => {
          const sub = subMap[key]
          if (sub && sub.symbol && formatSymbol(sub.symbol) === pair.value &&
              sub.resolution && resolutionMap[sub.resolution] === '1M') {
            monthlySubscription = sub
            subscriptionKey = key
          }
        })
      }

      monthlySubscriptionCache = {
        key: subscriptionKey,
        subscription: monthlySubscription,
        pair: pair.value
      }
    }

    if (monthlySubscription && last && val2 && val2.currentPair &&
        formatSymbol(monthlySubscription.symbol) === val2.currentPair &&
        val2.currentPeriod === '1M' && val2.time && val2.open !== undefined) {

      const monthlyStateKey = `${pair.value}_#_1M`
      const validTime = getValidTimestamp(val2.time, monthlyStateKey, '1M')

      const resultVal = {
        time: validTime,
        open: safeNumber(val2.open),
        high: safeNumber(val2.high),
        low: safeNumber(val2.low),
        close: safeNumber(last),
        volume: safeNumber(val2.volume)
      }

      lastCompleteBar.value[monthlyStateKey] = {
        open: safeNumber(val2.open),
        high: safeNumber(val2.high),
        low: safeNumber(val2.low),
        volume: safeNumber(val2.volume)
      }

      monthlySubscription.listen(resultVal)
      return true
    }

    return false
  }

  watch([ticker, klineTicker], ([val1, val2]) => {
    if (handleMonthlyRealtimeUpdate(val1, val2)) {
      return
    }

    if (interval.value === '1M') {
      return
    }

    const key = `${pair.value}_#_${interval.value}`
    const last = (val1[pair.value] || {}).last

    if (subMap[key] && last && formatSymbol(subMap[key].symbol) === pair.value) {
      let resultVal

      if (val2 && val2.currentPair && val2.currentPeriod &&
          formatSymbol(subMap[key].symbol) === val2.currentPair &&
          interval.value === val2.currentPeriod) {
        const validTime = getValidTimestamp(val2.time, key, interval.value)
        resultVal = {
          time: validTime,
          open: safeNumber(val2.open),
          high: safeNumber(val2.high),
          low: safeNumber(val2.low),
          close: safeNumber(last),
          volume: safeNumber(val2.volume)
        }
        lastCompleteBar.value[key] = {
          open: safeNumber(val2.open),
          high: safeNumber(val2.high),
          low: safeNumber(val2.low),
          volume: safeNumber(val2.volume)
        }
      } else if (val2 && val2.currentPair && formatSymbol(subMap[key].symbol) === val2.currentPair) {
        const baseBar = lastCompleteBar.value[key] || {}
        const currentClose = safeNumber(last)
        const validTime = getValidTimestamp(val2.time || Date.now(), key, interval.value)
        resultVal = {
          time: validTime,
          open: safeNumber(val2.open, baseBar.open || currentClose),
          high: Math.max(safeNumber(val2.high, baseBar.high || currentClose), currentClose),
          low: Math.min(safeNumber(val2.low, baseBar.low || currentClose), currentClose),
          close: currentClose,
          volume: safeNumber(val2.volume, baseBar.volume)
        }
      } else if (lastCompleteBar.value[key]) {
        const baseBar = lastCompleteBar.value[key]
        const currentClose = safeNumber(last)
        // 使用对齐的时间戳，确保时间戳递增
        const lastTime = lastBarTime.value[key] || 0
        const currentTime = Date.now()
        const nextTime = Math.max(currentTime, lastTime + 1000) // 至少间隔1秒
        const validTime = getValidTimestamp(nextTime, key, interval.value)
        resultVal = {
          time: validTime,
          open: baseBar.open,
          high: Math.max(baseBar.high, currentClose),
          low: Math.min(baseBar.low, currentClose),
          close: currentClose,
          volume: baseBar.volume
        }
      } else {
        const currentClose = safeNumber(last)
        // 使用对齐的时间戳，确保时间戳递增
        const lastTime = lastBarTime.value[key] || 0
        const currentTime = Date.now()
        const nextTime = Math.max(currentTime, lastTime + 1000) // 至少间隔1秒
        const validTime = getValidTimestamp(nextTime, key, interval.value)
        resultVal = {
          time: validTime,
          open: currentClose,
          high: currentClose,
          low: currentClose,
          close: currentClose,
          volume: 0
        }
      }

      if (rafId) {
        cancelAnimationFrame(rafId)
      }

      rafId = requestAnimationFrame(() => {
        if (subMap[key] && subMap[key].listen) {
          subMap[key].listen(resultVal)
        }
        rafId = null
      })
    }
  }, { deep: true })

  function subscribeBars(symbolInfo: any, resolution: any, onRealtimeCallback: any, subscriberUID: any, onResetCacheNeededCallback: any) {
    const subscriptionKey = `${symbolInfo.fullName}_#_${resolutionMap[resolution]}`

    // 检查是否是周期切换
    const isResolutionChange = Object.keys(subMap).some(key => {
      const sub = subMap[key]
      return sub && sub.symbol === symbolInfo.fullName && sub.resolution !== resolution
    })

    if (subMap[subscriptionKey]) {
      delete subMap[subscriptionKey]
    }
    if (subMap[subscriberUID]) {
      const oldKey = subMap[subscriberUID]
      if (typeof oldKey === 'string' && subMap[oldKey]) {
        delete subMap[oldKey]
      }
      delete subMap[subscriberUID]
    }

    if (resolutionMap[resolution] === '1M') {
      monthlySubscriptionCache = {
        key: null,
        subscription: null,
        pair: null
      }
    }

    const currentKey = `${symbolInfo.fullName}_#_${resolutionMap[resolution]}`
    Object.keys(lastCompleteBar.value).forEach(key => {
      if (key !== currentKey) {
        delete lastCompleteBar.value[key]
      }
    })

    // 清理不同周期的时间戳记录，避免时间违规
    Object.keys(lastBarTime.value).forEach(key => {
      if (key !== currentKey) {
        delete lastBarTime.value[key]
      }
    })

    // 如果是周期切换，强制清理所有相关缓存
    if (isResolutionChange) {
      // 清理数据缓存
      const symbolPrefix = symbolInfo.fullName
      Array.from(dataCache.keys()).forEach(cacheKey => {
        if (cacheKey.startsWith(symbolPrefix)) {
          dataCache.delete(cacheKey)
        }
      })

      // 调用TradingView的重置缓存回调
      if (onResetCacheNeededCallback) {
        try {
          onResetCacheNeededCallback()
        } catch (error) {
          console.warn('Error calling onResetCacheNeededCallback:', error)
        }
      }
    }

    const subscription = {
      resolution,
      symbol: symbolInfo.fullName,
      listen: (newPriceData) => {
        try {
          onRealtimeCallback(newPriceData)
        } catch (error) {
          console.error('Error in realtime callback:', error)
        }
      }
    }

    subMap[subscriptionKey] = subscription
    subMap[subscriberUID] = subscriptionKey

    if (resolutionMap[resolution] === '1M') {
      monthlySubscriptionCache = {
        key: subscriptionKey,
        subscription: subscription,
        pair: symbolInfo.fullName
      }
    }
  }

  return {
    historyCallback: () => {},
    onReady: (cb: any) => {
      const config = {
        supported_resolutions: [
          '1',
          '3',
          '5',
          '15',
          '30',
          '60',
          '120',
          '240',
          '360',
          '480',
          '720',
          '1D',
          '3D',
          '1W',
          '1M'
        ]
      }
      const timer = setTimeout(() => {
        cb(config)
        clearTimeout(timer)
      }, 0)
    },
    resolveSymbol(symbolName: string, onSymbolResolveCallback: any) {
      let pricescaleValue = pairInfo[symbolName]?.price_scale || 8
      pricescaleValue = pricescaleValue > 16 ? 16 : pricescaleValue
      const symbolInfo = {
        symbol: symbolName.includes('SWAP') ? symbolName.replace('_SWAP', '').replace('_', '') : symbolName.replace('_', '/'),
        name: symbolName,
        ticker: symbolName,
        fullName: symbolName,
        discription: '',
        exchange: 'KTX',
        type: 'Spot',
        has_intraday: true,
        minmov: 1,
        minmove2: 0,
        pricescale: Math.pow(10, pricescaleValue),
        timezone: 'Etc/UTC', // 时区
        session: '0000-2400:2345671;1',
        volume_precision: 2,
        has_weekly_and_monthly: true,
        has_empty_bars: true
      }
      const timer = setTimeout(() => {
        onSymbolResolveCallback(symbolInfo)
        clearTimeout(timer)
      }, 0)
    },
    getBars,
    subscribeBars,
    unsubscribeBars(subscriberUID: string) {
      if (rafId) {
        cancelAnimationFrame(rafId)
        rafId = null
      }

      const subscriptionKey = subMap[subscriberUID]

      if (subscriptionKey && typeof subscriptionKey === 'string') {
        const subscription = subMap[subscriptionKey]
        if (subscription && subscription.resolution && resolutionMap[subscription.resolution] === '1M') {
          monthlySubscriptionCache = {
            key: null,
            subscription: null,
            pair: null
          }
        }
        delete subMap[subscriptionKey]
      }
      delete subMap[subscriberUID]
    },
    clearCache,
    setForceRefresh
  }
}